import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { getSystemInfoTool } from "./get-system-info/index.js";
import { listAllowedCommandsTool } from "./list-allowed-commands/index.js";

// Local 工具列表
const localTools = [
  getSystemInfoTool,
  listAllowedCommandsTool
];

/**
 * 注册所有 Local 工具到 MCP 服务器
 */
export function registerLocalTools(server: McpServer) {
  localTools.forEach(tool => {
    server.registerTool(
      tool.name,
      tool.config,
      tool.handler
    );
  });
}

/**
 * 获取所有 Local 工具的信息
 */
export function getLocalToolsInfo() {
  return localTools.map(tool => ({
    name: tool.name,
    title: tool.config.title,
    description: tool.config.description
  }));
}

export { localTools };

#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { registerTools } from "./tools/index.js";
import { debug } from "./lib/debug.js";

// Load .env from the current working directory
config({ path: resolve(process.cwd(), ".env") });

// 创建 MCP 服务器
const server = new McpServer({
  name: "shell-executor",
  version: "1.0.0"
});

// 注册所有工具
registerTools(server);

// 启动服务器
async function main() {
  debug.info("Starting MCP server...");

  const transport = new StdioServerTransport();
  await server.connect(transport);

  debug.info("MCP server started successfully");
}

// 错误处理 - 使用文件日志而不是console输出
process.on('uncaughtException', (error) => {
  debug.error("Uncaught Exception", error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  debug.error("Unhandled Rejection", { reason, promise });
  process.exit(1);
});

// 启动服务器
main().catch((error) => {
  debug.error("Failed to start server", error);
  process.exit(1);
});

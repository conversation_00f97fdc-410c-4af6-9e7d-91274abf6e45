## TODO

[ ] 数据安全问题？A: 都在本地，不存在数据泄漏的防线
[ ] 不用考虑规模化带来的问题，可以很灵活的定制很多功能
[ ] 一个可执行程序在本地，然后远程连接页面。
    .好处是可执行程序只需要有基础能力就可以了，然后前端的话可以灵活的给用户修改，用户不用重新下载
    .很多一部分是可以重复用的，减少了二次开发的成本。但是可以满足各种用户的需求
[ ] 安全问题，执行必须落记录

## 为什么不单独使用Augment

[ ] 有什么会罢工
[ ] 打字太慢了，希望支持语音
[ ] 不希望什么都是我去规划，我希望的效果是，我给AI描述一个功能，AI自己规划，然后AI帮我实现，还有一个AI代表我去当监工

<!-- figd_Ga5Ih8Q-at57zn3LsE7IO3oADLTA_YeoNrLnzNd4 -->

1. 展示图标，不要使用background-image属性，直接使用img标签
2. 所有的img标签，都要设置display: block
3. 生成dom的时候，class为节点名称

3. 增加校验机制，校验节点的大小，位置，文本的字体颜色和大小，节点的层级关系，覆盖关系

二、单个页面交互 + 数据流设计环节
【注】：需要给AI提供图片，辅助它推断动效
. 合理推断交互
. 合理推断动画效果
. 数据mock在本地
. 评估滚动

每个设计稿写完之后就是设计交互

三、整体页面跳转串联，也是需要AI推断。如果是手机端APP，需要检查底部导航是否要封装成组件，并且配置好点击跳转


四、设计后端数据库，MongoDB
【注】：需要给AI提供图片，辅助它推断业务逻辑，业务逻辑是可以互相串联的

四、初始化后端项目，包括CICD
import { writeFileSync, appendFileSync, existsSync } from 'fs';
import { resolve } from 'path';
const DEBUG_LOG_PATH = resolve(process.cwd(), 'debug.log');
/**
 * 调试日志工具 - 写入文件而不是console
 */
export class DebugLogger {
    static instance;
    enabled;
    constructor() {
        // 从环境变量控制是否启用调试
        this.enabled = process.env.DEBUG === 'true' || process.env.NODE_ENV === 'development';
        // 清空之前的日志文件
        if (this.enabled && existsSync(DEBUG_LOG_PATH)) {
            writeFileSync(DEBUG_LOG_PATH, '');
        }
    }
    static getInstance() {
        if (!DebugLogger.instance) {
            DebugLogger.instance = new DebugLogger();
        }
        return DebugLogger.instance;
    }
    formatMessage(level, message, data) {
        const timestamp = new Date().toISOString();
        const dataStr = data ? ` | Data: ${JSON.stringify(data, null, 2)}` : '';
        return `[${timestamp}] [${level}] ${message}${dataStr}\n`;
    }
    log(message, data) {
        if (!this.enabled)
            return;
        try {
            const logMessage = this.formatMessage('LOG', message, data);
            appendFileSync(DEBUG_LOG_PATH, logMessage);
        }
        catch (error) {
            // 静默处理文件写入错误
        }
    }
    error(message, error) {
        if (!this.enabled)
            return;
        try {
            const errorData = error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : error;
            const logMessage = this.formatMessage('ERROR', message, errorData);
            appendFileSync(DEBUG_LOG_PATH, logMessage);
        }
        catch (writeError) {
            // 静默处理文件写入错误
        }
    }
    warn(message, data) {
        if (!this.enabled)
            return;
        try {
            const logMessage = this.formatMessage('WARN', message, data);
            appendFileSync(DEBUG_LOG_PATH, logMessage);
        }
        catch (error) {
            // 静默处理文件写入错误
        }
    }
    info(message, data) {
        if (!this.enabled)
            return;
        try {
            const logMessage = this.formatMessage('INFO', message, data);
            appendFileSync(DEBUG_LOG_PATH, logMessage);
        }
        catch (error) {
            // 静默处理文件写入错误
        }
    }
}
// 导出单例实例
export const debug = DebugLogger.getInstance();
//# sourceMappingURL=debug.js.map
/**
 * 调试日志工具 - 写入文件而不是console
 */
export declare class DebugLogger {
    private static instance;
    private enabled;
    private constructor();
    static getInstance(): DebugLogger;
    private formatMessage;
    log(message: string, data?: any): void;
    error(message: string, error?: any): void;
    warn(message: string, data?: any): void;
    info(message: string, data?: any): void;
}
export declare const debug: DebugLogger;
//# sourceMappingURL=debug.d.ts.map
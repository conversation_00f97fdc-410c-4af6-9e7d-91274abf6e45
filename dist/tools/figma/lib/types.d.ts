export type FigmaAuthOptions = {
    figmaApiKey: string;
    figmaOAuthToken: string;
    useOAuth: boolean;
};
export interface BoundingBox {
    x: number;
    y: number;
    width: number;
    height: number;
}
export type CSSRGBAColor = `rgba(${number}, ${number}, ${number}, ${number})`;
export type CSSHexColor = `#${string}`;
export interface ColorValue {
    hex: string;
    opacity: number;
}
export type SimplifiedFill = {
    type?: string;
    hex?: string;
    rgba?: string;
    opacity?: number;
    imageRef?: string;
    scaleMode?: string;
    gradientHandlePositions?: any[];
    gradientStops?: {
        position: number;
        color: ColorValue | string;
    }[];
} | CSSRGBAColor | CSSHexColor;
export interface ComponentProperties {
    name: string;
    value: string;
    type: string;
}
export interface TextStyle {
    fontFamily?: string;
    fontWeight?: number;
    fontSize?: number;
    lineHeight?: string;
    letterSpacing?: string;
    textCase?: string;
    textAlignHorizontal?: string;
    textAlignVertical?: string;
}
export interface SimplifiedNode {
    id: string;
    name: string;
    type: string;
    boundingBox?: BoundingBox;
    text?: string;
    textStyle?: string;
    style?: TextStyle;
    fills?: string;
    styles?: string;
    strokes?: string;
    effects?: string;
    opacity?: number;
    borderRadius?: string;
    layout?: string;
    componentId?: string;
    componentProperties?: ComponentProperties[];
    children?: SimplifiedNode[];
}
export interface SimplifiedDesign {
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    nodes: SimplifiedNode[];
    components: Record<string, any>;
    componentSets: Record<string, any>;
    globalVars: GlobalVars;
}
export type GlobalVars = {
    styles: Record<string, any>;
};
export type FetchImageParams = {
    nodeId: string;
    fileName: string;
    fileType: "png" | "svg";
};
export type FetchImageFillParams = Omit<FetchImageParams, "fileType"> & {
    imageRef: string;
};
export interface SvgOptions {
    outlineText: boolean;
    includeId: boolean;
    simplifyStroke: boolean;
}
export interface GetFileResponse {
    document: any;
    components: Record<string, any>;
    componentSets: Record<string, any>;
    schemaVersion: number;
    styles: Record<string, any>;
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    version: string;
    role: string;
    editorType: string;
    linkAccess: string;
}
export interface GetFileNodesResponse {
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    version: string;
    role: string;
    editorType: string;
    linkAccess: string;
    nodes: Record<string, {
        document: any;
        components?: Record<string, any>;
        componentSets?: Record<string, any>;
        schemaVersion: number;
        styles?: Record<string, any>;
    }>;
}
export interface GetImagesResponse {
    err?: string;
    images?: Record<string, string>;
    status?: number;
}
export interface GetImageFillsResponse {
    meta: {
        images?: Record<string, string>;
    };
}
//# sourceMappingURL=types.d.ts.map
{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/lib/parser.ts"], "names": [], "mappings": "AAQA,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE9E;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAA4C;IAC7E,MAAM,oBAAoB,GAAwB,EAAE,CAAC;IACrD,MAAM,uBAAuB,GAAwB,EAAE,CAAC;IACxD,IAAI,YAAwB,CAAC;IAE7B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,uBAAuB;QACvB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACrC,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC/B,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;YACrE,CAAC;QACH,CAAC,CAAC,CAAC;QACH,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACN,kBAAkB;QAClB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC;QACD,YAAY,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAqB,CAAC;IAEtG,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,YAAY,EAAE,IAAI,CAAC,YAAY;QAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;QAC/B,KAAK,EAAE,eAAe;QACtB,UAAU,EAAE,oBAAoB;QAChC,aAAa,EAAE,uBAAuB;QACtC,UAAU,EAAE;YACV,MAAM,EAAE,EAAE,CAAC,aAAa;SACzB;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,IAAS;IAC1B,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,cAAc,GAAmB;QACrC,EAAE,EAAE,IAAI,CAAC,EAAE;QACX,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAC;IAEF,QAAQ;IACR,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,cAAc,CAAC,WAAW,GAAG;YAC3B,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7B,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7B,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK;YACrC,MAAM,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM;SACxC,CAAC;IACJ,CAAC;IAED,OAAO;IACP,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;IACxC,CAAC;IAED,SAAS;IACT,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,SAAS,GAAc;YAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,UAAU,EAAE,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ;gBAC9C,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,QAAQ,IAAI;gBAC5C,CAAC,CAAC,SAAS;YACb,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ;gBAC/E,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG;gBACpD,CAAC,CAAC,SAAS;YACb,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,mBAAmB,EAAE,KAAK,CAAC,mBAAmB;YAC9C,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;SAC3C,CAAC;QAEF,iBAAiB;QACjB,cAAc,CAAC,KAAK,GAAG,SAAS,CAAC;IACnC,CAAC;IAED,OAAO;IACP,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,OAAO;IACP,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED,QAAQ;IACR,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;QACjD,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IACxC,CAAC;IAED,OAAO;IACP,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QAChC,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;SAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpE,CAAC;IAED,SAAS;IACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IAChD,CAAC;IAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,cAAc,CAAC,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAgB,EAAE,EAAE,CAAC,CAAC;YAClH,IAAI;YACJ,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,QAAQ;IACR,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAqB,CAAC;QAClF,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACrC,CAAC;IACH,CAAC;IAED,OAAO,eAAe,CAAC,cAAc,CAAmB,CAAC;AAC3D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,IAAS;IAC7C,MAAM,MAAM,GAAQ,EAAE,CAAC;IAEvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACtC,CAAC;IAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;IAC5D,CAAC;IAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;IAC5D,CAAC;IAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;IAC5D,CAAC;IAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;IAC5D,CAAC;IAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;QACnF,MAAM,CAAC,OAAO,GAAG;YACf,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC;YAC3B,KAAK,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC;YAC7B,GAAG,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;YACzB,MAAM,EAAE,IAAI,CAAC,aAAa,IAAI,CAAC;SAChC,CAAC;IACJ,CAAC;IAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACrB,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACxC,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;AACxD,CAAC"}
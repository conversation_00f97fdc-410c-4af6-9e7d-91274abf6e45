import { z } from "zod";
export declare const getFigmaDataTool: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: {
            fileKey: z.ZodString;
            nodeId: z.ZodOptional<z.ZodString>;
            depth: z.ZodOptional<z.ZodNumber>;
            extractDownloadableNodes: z.ZodOptional<z.ZodBoolean>;
        };
    };
    handler: (args: any) => Promise<{
        isError: boolean;
        content: {
            type: "text";
            text: string;
        }[];
    } | {
        content: {
            type: "text";
            text: string;
        }[];
        isError?: undefined;
    }>;
};
//# sourceMappingURL=index.d.ts.map